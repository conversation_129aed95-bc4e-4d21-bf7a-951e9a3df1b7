# frozen_string_literal: true

class ConsultationRequest < ActiveRecord::Base
  belongs_to :student, class_name: 'User'
  belongs_to :faculty, class_name: 'User'
  belongs_to :faculty_time_slot
  belongs_to :approved_by, class_name: 'User', optional: true
  has_one :consultation_summary, dependent: :destroy

  NATURE_OF_CONCERNS = %w[Personal Academic Teacher-related Co-students Family Others].freeze
  STATUSES = %w[pending approved declined completed cancelled].freeze

  validates :student_name, :student_number, :description, :nature_of_concern, :status, presence: true
  validates :student_name, length: { maximum: 255 }
  validates :student_number, length: { maximum: 50 }
  validates :nature_of_concern, inclusion: { in: NATURE_OF_CONCERNS }
  validates :status, inclusion: { in: STATUSES }
  validates :custom_concern, presence: true, if: -> { nature_of_concern == 'Others' }
  validates :custom_concern, length: { maximum: 1000 }, allow_blank: true
  validates :preferred_datetime, presence: true
  validates :faculty_comment, length: { maximum: 1000 }, allow_blank: true

  # Validations for new fields
  validates :college_campus_institute, length: { maximum: 255 }, allow_blank: true
  validates :department_program, length: { maximum: 255 }, allow_blank: true
  validates :semester, length: { maximum: 50 }, allow_blank: true
  validates :academic_year, length: { maximum: 20 }, allow_blank: true
  validates :place_of_consultation, length: { maximum: 255 }, allow_blank: true
  validates :prepared_by_name, length: { maximum: 255 }, allow_blank: true
  validates :prepared_by_designation, length: { maximum: 255 }, allow_blank: true
  validates :noted_by_program_chair, length: { maximum: 255 }, allow_blank: true
  validates :noted_by_college_dean, length: { maximum: 255 }, allow_blank: true
  validates :scf_number, length: { maximum: 50 }, allow_blank: true, uniqueness: true

  validate :preferred_datetime_in_future
  validate :preferred_datetime_matches_time_slot
  # validate :student_user_validation
  validate :faculty_user_validation
  validate :no_double_booking
  validate :status_transition_validation
  validate :description_content_validation
  validate :preferred_datetime_business_hours
  # validate :no_duplicate_pending_requests

  scope :pending, -> { where(status: 'pending') }
  scope :approved, -> { where(status: 'approved') }
  scope :declined, -> { where(status: 'declined') }
  scope :completed, -> { where(status: 'completed') }
  scope :cancelled, -> { where(status: 'cancelled') }
  scope :for_student, ->(student) { where(student: student) }
  scope :for_faculty, ->(faculty) { where(faculty: faculty) }
  scope :by_concern, ->(concern) { where(nature_of_concern: concern) }
  scope :recent, -> { order(created_at: :desc) }
  scope :upcoming, -> { where('preferred_datetime > ?', Time.current) }

  before_validation :set_student_info, on: :create
  before_validation :generate_scf_number, on: :create
  after_create :send_submission_notification
  after_update :create_consultation_summary, if: :saved_change_to_status?
  after_update :send_status_notification, if: :saved_change_to_status?
  after_update :sync_calendar_event, if: :saved_change_to_status?

  # State machine methods
  def approve!(approver, comment = nil)
    transaction do
      update!(
        status: 'approved',
        approved_by: approver,
        approved_at: Time.current,
        faculty_comment: comment
      )
    end
  end

  def decline!(approver, comment)
    transaction do
      update!(
        status: 'declined',
        approved_by: approver,
        declined_at: Time.current,
        faculty_comment: comment
      )
    end
  end

  def complete!(completion_notes = nil)
    transaction do
      update!(
        status: 'completed',
        completed_at: Time.current,
        faculty_comment: [faculty_comment, completion_notes].compact.join("\n\n")
      )
    end
  end

  def cancel!
    update!(status: 'cancelled')
  end

  # Status check methods
  def pending?
    status == 'pending'
  end

  def approved?
    status == 'approved'
  end

  def declined?
    status == 'declined'
  end

  def completed?
    status == 'completed'
  end

  def cancelled?
    status == 'cancelled'
  end

  def can_be_approved?
    pending? && preferred_datetime > Time.current
  end

  def can_be_declined?
    pending?
  end

  def can_be_completed?
    approved? && preferred_datetime <= Time.current
  end

  # Utility methods
  def formatted_preferred_datetime
    preferred_datetime.strftime('%B %d, %Y at %I:%M %p')
  end

  def concern_type_display
    nature_of_concern.humanize
  end

  def status_display
    status.humanize
  end

  def duration_in_minutes
    30 # Default consultation duration
  end

  def end_datetime
    preferred_datetime + duration_in_minutes.minutes
  end

  # Class methods for reporting
  def self.by_concern_type_for_faculty(faculty_user)
    for_faculty(faculty_user)
      .group(:nature_of_concern)
      .count
  end

  def self.completed_in_date_range(start_date, end_date)
    completed.where(completed_at: start_date.beginning_of_day..end_date.end_of_day)
  end

  def self.pending_for_faculty(faculty_user)
    for_faculty(faculty_user).pending.upcoming.recent
  end

  private

  def set_student_info
    return unless student

    self.student_name ||= student.name
    # Try to get student number from student record or user login_id
    if student.respond_to?(:student) && student.student
      self.student_number ||= student.student.id.to_s
    else
      # Fallback to user's login_id or a generated ID
      self.student_number ||= student.pseudonyms.first&.unique_id || "STU#{student.id}"
    end
  end

  def generate_scf_number
    return if scf_number.present?

    # Generate SCF number in format: SCF-YYYY-NNNN
    year = Date.current.year
    sequence = ConsultationRequest.where("scf_number LIKE ?", "SCF-#{year}-%").count + 1
    self.scf_number = "SCF-#{year}-#{sequence.to_s.rjust(4, '0')}"
  end

  def preferred_datetime_in_future
    return unless preferred_datetime

    if preferred_datetime <= Time.current
      errors.add(:preferred_datetime, 'must be in the future')
    end
  end

  def preferred_datetime_matches_time_slot
    return unless faculty_time_slot && preferred_datetime

    unless faculty_time_slot.available_at?(preferred_datetime)
      errors.add(:preferred_datetime, 'is not available in the selected time slot')
    end
  end

  # def student_user_validation
  #   return unless student

  #   # Check if user has student enrollment
  #   unless student.enrollments.where(type: 'StudentEnrollment', workflow_state: 'active').exists?
  #     errors.add(:student, 'must be an active student')
  #   end
  # end

  def faculty_user_validation
    return unless faculty

    # Check if user has faculty role (teacher enrollment)
    unless faculty.enrollments.where(type: 'TeacherEnrollment', workflow_state: 'active').exists?
      errors.add(:faculty, 'must be a faculty member')
    end
  end

  def no_double_booking
    return unless faculty_time_slot && preferred_datetime && status.in?(['pending', 'approved'])

    if faculty_time_slot.booked_at?(preferred_datetime)
      errors.add(:preferred_datetime, 'is already booked')
    end
  end

  def status_transition_validation
    return unless status_changed?

    old_status = status_was
    new_status = status

    valid_transitions = {
      'pending' => %w[approved declined cancelled],
      'approved' => %w[completed cancelled],
      'declined' => %w[pending], # Allow re-submission
      'completed' => [], # Final state
      'cancelled' => %w[pending] # Allow re-submission
    }

    unless valid_transitions[old_status]&.include?(new_status)
      errors.add(:status, "cannot transition from #{old_status} to #{new_status}")
    end
  end

  def description_content_validation
    return unless description.present?

    # Check for minimum length
    if description.strip.length < 10
      errors.add(:description, 'must be at least 10 characters long')
    end

    # Check for maximum length
    if description.length > 2000
      errors.add(:description, 'cannot exceed 2000 characters')
    end

    # Check for inappropriate content (basic check)
    inappropriate_words = %w[spam test testing hello hi]
    if inappropriate_words.any? { |word| description.downcase.strip == word }
      errors.add(:description, 'must provide a meaningful description of your concern')
    end
  end

  def preferred_datetime_business_hours
    return unless preferred_datetime

    # Check if the requested time is during reasonable business hours
    hour = preferred_datetime.hour
    day_of_week = preferred_datetime.wday

    # # Monday = 1, Sunday = 0
    # if day_of_week == 0 || day_of_week == 7
    #   errors.add(:preferred_datetime, 'must be during weekdays (Monday-Saturdays)')
    # end

    if hour < 6 || hour >= 20
      errors.add(:preferred_datetime, 'must be during business hours (6 AM - 8 PM)')
    end
  end

  # def no_duplicate_pending_requests
  #   return unless student && faculty && status == 'pending'

  #   # Check for existing pending requests from the same student to the same faculty
  #   existing_request = ConsultationRequest.where(
  #     student: student,
  #     faculty: faculty,
  #     status: 'pending'
  #   ).where.not(id: id).exists?

  #   if existing_request
  #     errors.add(:base, 'You already have a pending consultation request with this faculty member')
  #   end

  #   # Check for too many pending requests from the same student
  #   total_pending = student.student_consultation_requests.pending.where.not(id: id).count
  #   if total_pending >= 3
  #     errors.add(:base, 'You cannot have more than 3 pending consultation requests at a time')
  #   end
  # end

  def create_consultation_summary
    return unless status == 'completed'

    ConsultationSummary.create!(
      consultation_request: self,
      faculty: faculty,
      student: student,
      student_name: student_name,
      student_id: student_id,
      consultation_date: completed_at || preferred_datetime,
      concern_type: nature_of_concern,
      description: description,
      faculty_notes: faculty_comment
    )
  end

  def send_submission_notification
    # Send notification to faculty when a new request is submitted
    ConsultationNotificationService.send_request_submitted_notification(self)
  rescue => e
    Rails.logger.error "Failed to send submission notification for consultation #{id}: #{e.message}"
  end

  def send_status_notification
    # Send appropriate notification based on status change
    case status
    when 'approved'
      ConsultationNotificationService.send_request_approved_notification(self)
    when 'declined'
      ConsultationNotificationService.send_request_declined_notification(self)
    when 'completed'
      ConsultationNotificationService.send_consultation_completed_notification(self)
    end
  rescue => e
    Rails.logger.error "Failed to send status notification for consultation #{id}: #{e.message}"
  end

  def sync_calendar_event
    # Sync consultation with calendar system
    case status
    when 'approved'
      ConsultationCalendarService.create_calendar_event_for_consultation(self)
    when 'completed', 'cancelled', 'declined'
      ConsultationCalendarService.update_calendar_event_for_consultation(self)
    end
  rescue => e
    Rails.logger.error "Failed to sync consultation #{id} with calendar: #{e.message}"
  end
end
