import React, { useState } from 'react'
import { View } from '@instructure/ui-view'
import { Button } from '@instructure/ui-buttons'
import { Grid } from '@instructure/ui-grid'
import { IconSearchLine, IconXLine } from '@instructure/ui-icons'
import type { ConsultationFilters } from '../types'

interface SummariesFiltersProps {
  filters: ConsultationFilters
  concernTypes: string[]
  onFiltersChange: (filters: ConsultationFilters) => void
  loading: boolean
}

const SummariesFilters: React.FC<SummariesFiltersProps> = ({
  filters,
  concernTypes,
  onFiltersChange,
  loading
}) => {
  const [localFilters, setLocalFilters] = useState<ConsultationFilters>(filters)

  const handleFilterChange = (key: keyof ConsultationFilters, value: string) => {
    setLocalFilters(prev => ({
      ...prev,
      [key]: value
    }))
  }

  const handleApplyFilters = () => {
    onFiltersChange(localFilters)
  }

  const handleClearFilters = () => {
    const clearedFilters = {}
    setLocalFilters(clearedFilters)
    onFiltersChange(clearedFilters)
  }

  const hasActiveFilters = Object.values(localFilters).some(value => value && value !== '')

  return (
    <View as="div" background="secondary" padding="medium" borderRadius="medium" margin="0 0 medium 0">
      <Grid>
        <Grid.Row>
          <div style={{ display: 'flex', flexDirection: 'column', width: '270px', marginRight: '10px' }}>
            <label htmlFor="concern-type-select" style={{ display: 'block', marginBottom: '0.5rem', fontSize: '0.875rem', fontWeight: '500' }}>
              Concern Type
            </label>
            <select
              id="concern-type-select"
              value={localFilters.concern_type || ''}
              onChange={(e) => handleFilterChange('concern_type', e.target.value)}
              style={{
                width: '270px',
                padding: '0.5rem',
                border: '1px solid #C7CDD1',
                borderRadius: '0.25rem',
                fontSize: '1rem',
                backgroundColor: '#FFFFFF'
              }}
            >
              <option value="">All Types</option>
              {concernTypes.map(type => (
                <option key={type} value={type}>
                  {type}
                </option>
              ))}
            </select>
          </div>

        
          <div style={{ width: '270px', marginRight: '10px' }}>
            <label htmlFor="student-search-input" style={{ display: 'block', marginBottom: '0.5rem', fontSize: '0.875rem', fontWeight: '500' }}>
              Search Students
            </label>
            <input
              id="student-search-input"
              type="text"
              placeholder="Student name or ID"
              value={localFilters.student_search || ''}
              onChange={(e) => handleFilterChange('student_search', e.target.value)}
              style={{
                width: '252px',
                padding: '0.5rem',
                border: '1px solid #C7CDD1',
                borderRadius: '0.25rem',
                fontSize: '1rem',
                backgroundColor: '#FFFFFF'
              }}
            />
          </div>

          <div style={{ width: '270px', marginRight: '10px' }}>
            <label htmlFor="content-search-input" style={{ display: 'block', marginBottom: '0.5rem', fontSize: '0.875rem', fontWeight: '500' }}>
              Search Content
            </label>
            <input
              id="content-search-input"
              type="text"
              placeholder="Notes, outcomes, etc."
              value={localFilters.content_search || ''}
              onChange={(e) => handleFilterChange('content_search', e.target.value)}
              style={{
                width: '252px',
                padding: '0.5rem',
                border: '1px solid #C7CDD1',
                borderRadius: '0.25rem',
                fontSize: '1rem',
                backgroundColor: '#FFFFFF'
              }}
            />
          </div>

          <div style={{ alignSelf: 'end', paddingBottom: '13px' }}>
            <Button
              color="primary"
              renderIcon={<IconSearchLine />}
              onClick={handleApplyFilters}
              disabled={loading}
              style={{ marginRight: '10px' }}
            >
              Search
            </Button>
            {hasActiveFilters && (
              <Button
                renderIcon={<IconXLine />}
                onClick={handleClearFilters}
                disabled={loading}
              >
                Clear
              </Button>
            )}
          </div>
        </Grid.Row>
        
        <Grid.Row>
          <div style={{ width: '270px', marginRight: '10px' }}>
            <label htmlFor="start-date-input" style={{ display: 'block', marginBottom: '0.5rem', fontSize: '0.875rem', fontWeight: '500' }}>
              Start Date
            </label>
            <input
              id="start-date-input"
              type="date"
              value={localFilters.start_date || ''}
              onChange={(e) => handleFilterChange('start_date', e.target.value)}
              style={{
                width: '252px',
                padding: '0.5rem',
                border: '1px solid #C7CDD1',
                borderRadius: '0.25rem',
                fontSize: '1rem',
                backgroundColor: '#FFFFFF'
              }}
            />
          </div>

          <div style={{ width: '270px', marginRight: '10px' }}>
            <label htmlFor="end-date-input" style={{ display: 'block', marginBottom: '0.5rem', fontSize: '0.875rem', fontWeight: '500' }}>
              End Date
            </label>
            <input
              id="end-date-input"
              type="date"
              value={localFilters.end_date || ''}
              onChange={(e) => handleFilterChange('end_date', e.target.value)}
              style={{
                width: '252px',
                padding: '0.5rem',
                border: '1px solid #C7CDD1',
                borderRadius: '0.25rem',
                fontSize: '1rem',
                backgroundColor: '#FFFFFF'
              }}
            />
          </div>

         
          <div
            style={{
              display: 'flex',
              alignItems: 'end',
              gap: '1rem',
              marginRight: '1rem',
              marginTop: '1.75rem',
              paddingBottom: '10px'
            }}
          >
            <label style={{ display: 'flex', alignItems: 'center' }}>
              <input
                style={{ margin: 0 }}
                type="checkbox"
                checked={localFilters.with_referrals === 'true'}
                onChange={(e) => handleFilterChange('with_referrals', e.target.checked ? 'true' : '')}
              />
              <span style={{ marginLeft: '0.5rem' }}>With Referrals</span>
            </label>
            <label style={{ display: 'flex', alignItems: 'center' }}>
              <input
                style={{ margin: 0 }}
                type="checkbox"
                checked={localFilters.requires_follow_up === 'true'}
                onChange={(e) => handleFilterChange('requires_follow_up', e.target.checked ? 'true' : '')}
              />
              <span style={{ marginLeft: '0.5rem' }}>Requires Follow-up</span>
            </label>
          </div>
        </Grid.Row>
      </Grid>
    </View>
  )
}

export default SummariesFilters
