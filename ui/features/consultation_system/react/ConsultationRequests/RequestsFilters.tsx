import React, { useState } from 'react'
import { View } from '@instructure/ui-view'
import { Button } from '@instructure/ui-buttons'
import { Grid } from '@instructure/ui-grid'
import { IconSearchLine, IconXLine } from '@instructure/ui-icons'
import type { ConsultationFilters } from '../types'

interface RequestsFiltersProps {
  filters: ConsultationFilters
  concernTypes: string[]
  statuses: string[]
  userRole: 'student' | 'faculty'
  onFiltersChange: (filters: ConsultationFilters) => void
  loading: boolean
}

const RequestsFilters: React.FC<RequestsFiltersProps> = ({
  filters,
  concernTypes,
  statuses,
  userRole,
  onFiltersChange,
  loading
}) => {
  const [localFilters, setLocalFilters] = useState<ConsultationFilters>(filters)

  const handleFilterChange = (key: keyof ConsultationFilters, value: string) => {
    setLocalFilters(prev => ({
      ...prev,
      [key]: value
    }))
  }


  const handleApplyFilters = () => {
    onFiltersChange(localFilters)
  }

  const handleClearFilters = () => {
    const clearedFilters = {}
    setLocalFilters(clearedFilters)
    onFiltersChange(clearedFilters)
  }

  const hasActiveFilters = Object.values(localFilters).some(value => value && value !== '')

  return (
    <View as="div" background="secondary" padding="medium" borderRadius="medium" margin="0 0 medium 0">
      <Grid>
        <Grid.Row>
          <div style={{ display: 'flex', flexDirection: 'column', width: '270px', marginRight: '10px' }}>
            <label htmlFor="status-select" style={{ display: 'block', marginBottom: '0.5rem', fontSize: '0.875rem', fontWeight: '500' }}>
              Status
            </label>
            <select
              id="status-select"
              value={localFilters.status || ''}
              onChange={(e) => handleFilterChange('status', e.target.value)}
              style={{
                width: '270px',
                padding: '0.5rem',
                border: '1px solid #C7CDD1',
                borderRadius: '0.25rem',
                fontSize: '1rem',
                backgroundColor: '#FFFFFF'
              }}
            >
              <option value="">All Statuses</option>
              {statuses.map(status => (
                <option key={status} value={status}>
                  {status.charAt(0).toUpperCase() + status.slice(1)}
                </option>
              ))}
            </select>
          </div>

          <div style={{ display: 'flex', flexDirection: 'column', width: '270px', marginRight: '10px' }}>
            <label htmlFor="concern-type-select" style={{ display: 'block', marginBottom: '0.5rem', fontSize: '0.875rem', fontWeight: '500' }}>
              Concern Type
            </label>
            <select
              id="concern-type-select"
              value={localFilters.concern_type || ''}
              onChange={(e) => handleFilterChange('concern_type', e.target.value)}
              style={{
                width: '270px',
                padding: '0.5rem',
                border: '1px solid #C7CDD1',
                borderRadius: '0.25rem',
                fontSize: '1rem',
                backgroundColor: '#FFFFFF'
              }}
            >
              <option value="">All Types</option>
              {concernTypes.map((type) => (
                <option key={type} value={type}>
                  {type}
                </option>
              ))}
            </select>
          </div>

          <div style={{ width: '270px', marginRight: '10px' }}>
            <label htmlFor="faculty-search-input" style={{ display: 'block', marginBottom: '0.5rem', fontSize: '0.875rem', fontWeight: '500' }}>
              {userRole === 'student' ? 'Search Faculty' : 'Search Students'}
            </label>
            <input
              id="faculty-search-input"
              type="text"
              placeholder={userRole === 'student' ? 'Faculty name' : 'Student name or ID'}
              value={localFilters.student_search || ''}
              onChange={(e) => handleFilterChange('student_search', e.target.value)}
              style={{
                width: '252px',
                padding: '0.5rem',
                border: '1px solid #C7CDD1',
                borderRadius: '0.25rem',
                fontSize: '1rem',
                backgroundColor: '#FFFFFF'
              }}
            />
          </div>

          <div style={{ alignSelf: 'end', paddingBottom: '13px' }}>
            <Button
              color="primary"
              renderIcon={<IconSearchLine />}
              onClick={handleApplyFilters}
              disabled={loading}
              style={{ marginRight: '10px' }}
            >
              Search
            </Button>
            {hasActiveFilters && (
              <Button
                renderIcon={<IconXLine />}
                onClick={handleClearFilters}
                disabled={loading}
              >
                Clear
              </Button>
            )}
          </div>
        </Grid.Row>

        <Grid.Row>
          <div style={{ width: '270px', marginRight: '10px' }}>
            <label htmlFor="requests-start-date-input" style={{ display: 'block', marginBottom: '0.5rem', fontSize: '0.875rem', fontWeight: '500' }}>
              Start Date
            </label>
            <input
              id="requests-start-date-input"
              type="date"
              value={localFilters.start_date || ''}
              onChange={(e) => handleFilterChange('start_date', e.target.value)}
              style={{
                width: '252px',
                padding: '0.5rem',
                border: '1px solid #C7CDD1',
                borderRadius: '0.25rem',
                fontSize: '1rem',
                backgroundColor: '#FFFFFF'
              }}
            />
          </div>

          <div style={{ width: '270px', marginRight: '10px' }}>
            <label htmlFor="requests-end-date-input" style={{ display: 'block', marginBottom: '0.5rem', fontSize: '0.875rem', fontWeight: '500' }}>
              End Date
            </label>
            <input
              id="requests-end-date-input"
              type="date"
              value={localFilters.end_date || ''}
              onChange={(e) => handleFilterChange('end_date', e.target.value)}
              style={{
                width: '252px',
                padding: '0.5rem',
                border: '1px solid #C7CDD1',
                borderRadius: '0.25rem',
                fontSize: '1rem',
                backgroundColor: '#FFFFFF'
              }}
            />
          </div>

          <div
            style={{
              display: 'flex',
              alignItems: 'end',
              gap: '1rem',
              marginRight: '1rem',
              marginTop: '1.75rem',
              paddingBottom: '10px'
            }}
          >
            <label style={{ display: 'flex', alignItems: 'center' }}>
              <input
                style={{ margin: 0 }}
                type="checkbox"
                checked={localFilters.content_search === 'urgent'}
                onChange={(e) => handleFilterChange('content_search', e.target.checked ? 'urgent' : '')}
              />
              <span style={{ marginLeft: '0.5rem' }}>Urgent Requests</span>
            </label>
            <label style={{ display: 'flex', alignItems: 'center' }}>
              <input
                style={{ margin: 0 }}
                type="checkbox"
                checked={localFilters.start_date === 'this_week'}
                onChange={(e) => handleFilterChange('start_date', e.target.checked ? 'this_week' : '')}
              />
              <span style={{ marginLeft: '0.5rem' }}>Upcoming This Week</span>
            </label>
          </div>
        </Grid.Row>
      </Grid>
    </View>
  )
}

export default RequestsFilters
