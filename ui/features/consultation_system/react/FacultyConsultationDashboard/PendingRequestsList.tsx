import React, { useState } from 'react'
import { View } from '@instructure/ui-view'
import { Heading } from '@instructure/ui-heading'
import { Button } from '@instructure/ui-buttons'
import { Text } from '@instructure/ui-text'
import { Badge } from '@instructure/ui-badge'
import { TextArea } from '@instructure/ui-text-area'
import { Modal } from '@instructure/ui-modal'
import { IconCheckMarkLine, IconXLine, IconUserLine, IconCalendarMonthLine } from '@instructure/ui-icons'
import type { ConsultationRequest } from '../types'

interface PendingRequestsListProps {
  requests: ConsultationRequest[]
  onApprove: (id: string, comment?: string) => Promise<void>
  onDecline: (id: string, comment: string) => Promise<void>
  loading: boolean
}

const PendingRequestsList: React.FC<PendingRequestsListProps> = ({
  requests,
  onApprove,
  onDecline,
  loading
}) => {
  const [selectedRequest, setSelectedRequest] = useState<ConsultationRequest | null>(null)
  const [modalType, setModalType] = useState<'approve' | 'decline' | null>(null)
  const [comment, setComment] = useState('')
  const [submitting, setSubmitting] = useState(false)

  const handleOpenModal = (request: ConsultationRequest, type: 'approve' | 'decline') => {
    setSelectedRequest(request)
    setModalType(type)
    setComment('')
  }

  const handleCloseModal = () => {
    setSelectedRequest(null)
    setModalType(null)
    setComment('')
  }

  const handleSubmit = async () => {
    if (!selectedRequest) return

    try {
      setSubmitting(true)
      
      if (modalType === 'approve') {
        await onApprove(selectedRequest.id, comment || undefined)
      } else if (modalType === 'decline') {
        if (!comment.trim()) {
          alert('Please provide a reason for declining this request.')
          return
        }
        await onDecline(selectedRequest.id, comment)
      }
      
      handleCloseModal()
    } catch (error) {
      // Error handling is done in parent component
    } finally {
      setSubmitting(false)
    }
  }

  const formatDateTime = (dateTimeString: string) => {
    try {
      const date = new Date(dateTimeString)
      return date.toLocaleDateString('en-US', {
        weekday: 'long',
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: 'numeric',
        minute: '2-digit',
        hour12: true
      })
    } catch {
      return dateTimeString
    }
  }

  if (requests.length === 0) {
    return (
      <View as="div" textAlign="center" padding="x-large">
        <div className="empty-state">
          <div className="empty-icon">
            <IconUserLine size="large" />
          </div>
          <Heading level="h3" margin="0 0 small 0">
            No Pending Requests
          </Heading>
          <Text>
            You don't have any pending consultation requests at the moment. 
            New requests will appear here when students submit them.
          </Text>
        </div>
      </View>
    )
  }

  return (
    <>
      <View as="div" margin="medium 0 0 0">
        <Heading level="h3" margin="0 0 medium 0">
          Pending Consultation Requests ({requests.length})
        </Heading>
        
        {requests.map(request => (
          <View
            key={request.id}
            as="div"
            background="primary"
            padding="medium"
            borderRadius="medium"
            borderWidth="small"
            borderColor="brand"
            margin="0 0 medium 0"
          >
            <div style={{ display: 'flex', justifyContent: 'space-between' }}>
              <View as="div" width="70%">
                <View as="div" display="flex">
                  <IconUserLine size="small" margin="0 small 0 0" />
                  <Heading level="h4" margin="0 0 0 x-small">
                    {request.student_name}
                  </Heading>
                  <Text size="small" color="secondary">
                    ({request.student_id})
                  </Text>
                </View>

                <View as="div" margin="0 0 small 0">
                  <IconCalendarMonthLine size="x-small" margin="0 small 0 0" />
                  <Text weight="bold">
                    {formatDateTime(request.preferred_datetime)}
                  </Text>
                </View>

                <View as="div" margin="0 0 small 0">
                  <Badge
                    type="count"
                    text={request.concern_type_display}
                  />
                  <Badge
                    type="notification"
                    text={request.status_display}
                    margin="0 0 0 x-small"
                  />
                </View>

                <View as="div" margin="small 0 0 0" padding="small" borderRadius="small">
                  <Text size="small" weight="bold">
                    Student's Concern:
                  </Text>
                  <Text size="small">
                    {request.description}
                  </Text>
                </View>

                <View as="div" margin="small 0 0 0">
                  <Text size="x-small" color="secondary">
                    Submitted: {new Date(request.created_at).toLocaleDateString('en-US', {
                      month: 'short',
                      day: 'numeric',
                      year: 'numeric',
                      hour: 'numeric',
                      minute: '2-digit'
                    })}
                  </Text>
                </View>
              </View>

              <div style={{ display: 'flex', flexDirection: 'row', gap: '0.5rem', alignItems: 'flex-start' }}>
                <Button
                  color="success"
                  size="small"
                  renderIcon={() => <IconCheckMarkLine />}
                  onClick={() => handleOpenModal(request, 'approve')}
                  disabled={loading}
                  margin="0 small 0 0"
                >
                  Approve
                </Button>
                <Button
                  color="danger"
                  size="small"
                  renderIcon={() => <IconXLine />}
                  onClick={() => handleOpenModal(request, 'decline')}
                  disabled={loading}
                >
                  Decline
                </Button>
              </div>
            </div>
          </View>
        ))}
      </View>

      <Modal
        open={modalType !== null}
        onDismiss={handleCloseModal}
        size="medium"
        label={modalType === 'approve' ? 'Approve Consultation Request' : 'Decline Consultation Request'}
      >
        <Modal.Header>
          <Heading level="h2">
            {modalType === 'approve' ? 'Approve' : 'Decline'} Consultation Request
          </Heading>
        </Modal.Header>
        
        <Modal.Body>
          {selectedRequest && (
            <View as="div">
              <View as="div" margin="0 0 medium 0">
                <Text weight="bold">Student:</Text> {selectedRequest.student_name} ({selectedRequest.student_id})
              </View>
              <View as="div" margin="0 0 medium 0">
                <Text weight="bold">Requested Time:</Text> {formatDateTime(selectedRequest.preferred_datetime)}
              </View>
              <View as="div" margin="0 0 medium 0">
                <Text weight="bold">Concern Type:</Text> {selectedRequest.concern_type_display}
              </View>
              
              <TextArea
                label={modalType === 'approve' ? 'Optional Comment' : 'Reason for Declining (Required)'}
                placeholder={
                  modalType === 'approve' 
                    ? 'Add any notes or instructions for the student...'
                    : 'Please explain why you are declining this request...'
                }
                value={comment}
                onChange={(e) => setComment(e.target.value)}
                height="6rem"
                required={modalType === 'decline'}
              />
            </View>
          )}
        </Modal.Body>
        
        <Modal.Footer>
          <Button onClick={handleCloseModal} disabled={submitting}>
            Cancel
          </Button>
          <Button
            color={modalType === 'approve' ? 'success' : 'danger'}
            onClick={handleSubmit}
            disabled={submitting || (modalType === 'decline' && !comment.trim())}
            margin="0 0 0 x-small"
          >
            {submitting ? 'Processing...' : (modalType === 'approve' ? 'Approve Request' : 'Decline Request')}
          </Button>
        </Modal.Footer>
      </Modal>
    </>
  )
}

export default PendingRequestsList
